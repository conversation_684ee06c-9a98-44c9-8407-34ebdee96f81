/**
 * 世界类
 * 负责管理实体和场景
 */
import { Engine } from './Engine';
import { Entity } from './Entity';
import { Scene } from '../scene/Scene';
import { System } from './System';
import { EventEmitter } from '../utils/EventEmitter';
import { generateUUID } from '../utils/UUID';

export class World extends EventEmitter {
  /** 引擎实例 */
  private engine: Engine;

  /** 实体映射 */
  private entities: Map<string, Entity> = new Map();

  /** 当前场景 */
  private activeScene: Scene | null = null;

  /** 场景映射 */
  private scenes: Map<string, Scene> = new Map();

  /** 系统列表 */
  private systems: System[] = [];

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建世界实例
   * @param engine 引擎实例
   */
  constructor(engine: Engine) {
    super();
    this.engine = engine;
  }

  /**
   * 初始化世界
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 创建默认场景
    const defaultScene = new Scene('默认场景');
    this.addScene(defaultScene);
    this.setActiveScene(defaultScene);

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 更新世界
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有系统
    for (const system of this.systems) {
      if (system.isEnabled()) {
        system.update(deltaTime);
      }
    }

    // 更新所有实体
    for (const entity of Array.from(this.entities.values())) {
      if (entity.isActive()) {
        entity.update(deltaTime);
      }
    }

    // 更新当前场景
    if (this.activeScene) {
      this.activeScene.update(deltaTime);
    }
  }

  /**
   * 固定时间步长更新
   * @param fixedDeltaTime 固定帧间隔时间（秒）
   */
  public fixedUpdate(fixedDeltaTime: number): void {
    // 固定更新所有系统
    for (const system of this.systems) {
      if (system.isEnabled()) {
        system.fixedUpdate(fixedDeltaTime);
      }
    }

    // 固定更新所有实体
    for (const entity of Array.from(this.entities.values())) {
      if (entity.isActive()) {
        entity.fixedUpdate(fixedDeltaTime);
      }
    }

    // 固定更新当前场景
    if (this.activeScene) {
      this.activeScene.fixedUpdate(fixedDeltaTime);
    }
  }

  /**
   * 创建实体
   * @param name 实体名称
   * @returns 创建的实体
   */
  public createEntity(name: string = '实体'): Entity {
    const entity = new Entity(name);
    this.addEntity(entity);
    return entity;
  }

  /**
   * 添加实体
   * @param entity 实体实例
   * @returns 添加的实体
   */
  public addEntity(entity: Entity): Entity {
    // 安全地设置实体ID，检查对象是否可扩展
    if (!entity.id) {
      try {
        entity.id = generateUUID();
      } catch (error) {
        // 如果对象不可扩展，尝试使用Object.defineProperty
        if (error.message.includes('not extensible') || error.message.includes('Cannot add property')) {
          try {
            Object.defineProperty(entity, 'id', {
              value: generateUUID(),
              writable: true,
              enumerable: true,
              configurable: true
            });
          } catch (defineError) {
            console.error('无法为实体设置ID，对象被冻结或密封:', defineError);
            // 如果仍然失败，创建一个新的实体对象并复制属性
            const newEntity = new Entity(entity.name || "实体");
            newEntity.id = generateUUID();
            // 复制其他属性
            newEntity.setActive(entity.isActive());
            // 复制标签
            const tags = entity.getTags();
            tags.forEach(tag => newEntity.addTag(tag));
            // 复制组件
            const components = entity.getAllComponents();
            components.forEach(component => {
              newEntity.addComponent(component);
            });
            entity = newEntity;
          }
        } else {
          throw error;
        }
      }
    }

    // 设置实体的世界引用
    entity.setWorld(this);

    // 添加到实体映射
    this.entities.set(entity.id, entity);

    // 如果有活跃场景，将实体添加到场景
    if (this.activeScene) {
      this.activeScene.addEntity(entity);
    }

    // 发出实体创建事件
    this.emit('entityCreated', entity);

    return entity;
  }

  /**
   * 获取实体
   * @param id 实体ID
   * @returns 实体实例，如果不存在则返回null
   */
  public getEntity(id: string): Entity | null {
    return this.entities.get(id) || null;
  }

  /**
   * 移除实体
   * @param entity 实体实例或ID
   * @returns 是否成功移除
   */
  public removeEntity(entity: Entity | string): boolean {
    const id = typeof entity === 'string' ? entity : entity.id;

    if (!id || !this.entities.has(id)) {
      return false;
    }

    const entityToRemove = this.entities.get(id)!;

    // 如果有活跃场景，从场景中移除实体
    if (this.activeScene) {
      this.activeScene.removeEntity(entityToRemove);
    }

    // 销毁实体
    (entityToRemove as any).dispose();

    // 从实体映射中移除
    this.entities.delete(id);

    // 发出实体移除事件
    this.emit('entityRemoved', entityToRemove);

    return true;
  }

  /**
   * 获取所有实体
   * @returns 实体数组
   */
  public getAllEntities(): Entity[] {
    return Array.from(this.entities.values());
  }

  /**
   * 根据名称查找实体
   * @param name 实体名称
   * @returns 匹配的实体数组
   */
  public findEntitiesByName(name: string): Entity[] {
    const result: Entity[] = [];

    for (const entity of Array.from(this.entities.values())) {
      if (entity.name === name) {
        result.push(entity);
      }
    }

    return result;
  }

  /**
   * 根据标签查找实体
   * @param tag 实体标签
   * @returns 匹配的实体数组
   */
  public findEntitiesByTag(tag: string): Entity[] {
    const result: Entity[] = [];

    for (const entity of Array.from(this.entities.values())) {
      if (entity.hasTag(tag)) {
        result.push(entity);
      }
    }

    return result;
  }

  /**
   * 创建场景
   * @param name 场景名称
   * @returns 创建的场景
   */
  public createScene(name: string = '场景'): Scene {
    const scene = new Scene(name);
    this.addScene(scene);
    return scene;
  }

  /**
   * 添加场景
   * @param scene 场景实例
   * @returns 添加的场景
   */
  public addScene(scene: Scene): Scene {
    // 如果场景已经有ID，则使用该ID，否则生成新ID
    if (!scene.id) {
      scene.id = generateUUID();
    }

    // 添加到场景映射
    this.scenes.set(scene.id, scene);

    // 发出场景添加事件
    this.emit('sceneAdded', scene);

    return scene;
  }

  /**
   * 获取场景
   * @param id 场景ID
   * @returns 场景实例，如果不存在则返回null
   */
  public getScene(id: string): Scene | null {
    return this.scenes.get(id) || null;
  }

  /**
   * 移除场景
   * @param scene 场景实例或ID
   * @returns 是否成功移除
   */
  public removeScene(scene: Scene | string): boolean {
    const id = typeof scene === 'string' ? scene : scene.id;

    if (!id || !this.scenes.has(id)) {
      return false;
    }

    const sceneToRemove = this.scenes.get(id)!;

    // 如果是当前活跃场景，则清除活跃场景
    if (this.activeScene === sceneToRemove) {
      this.activeScene = null;
    }

    // 销毁场景
    (sceneToRemove as any).dispose();

    // 从场景映射中移除
    this.scenes.delete(id);

    // 发出场景移除事件
    this.emit('sceneRemoved', sceneToRemove);

    return true;
  }

  /**
   * 获取所有场景
   * @returns 场景数组
   */
  public getAllScenes(): Scene[] {
    return Array.from(this.scenes.values());
  }

  /**
   * 设置活跃场景
   * @param scene 场景实例或ID
   * @returns 是否成功设置
   */
  public setActiveScene(scene: Scene | string): boolean {
    const targetScene = typeof scene === 'string' ? this.getScene(scene) : scene;

    if (!targetScene) {
      return false;
    }

    // 如果已经是活跃场景，则不做任何操作
    if (this.activeScene === targetScene) {
      return true;
    }

    // 保存旧场景
    const oldScene = this.activeScene;

    // 设置新的活跃场景
    this.activeScene = targetScene;

    // 发出场景切换事件
    this.emit('sceneChanged', targetScene, oldScene);

    return true;
  }

  /**
   * 获取活跃场景
   * @returns 活跃场景实例
   */
  public getActiveScene(): Scene | null {
    return this.activeScene;
  }

  /**
   * 获取引擎实例
   * @returns 引擎实例
   */
  public getEngine(): Engine {
    return this.engine;
  }

  /**
   * 添加系统
   * @param system 系统实例
   * @returns 添加的系统
   */
  public addSystem(system: System): System {
    // 设置系统的世界引用
    system.setWorld(this);

    // 添加到系统列表
    this.systems.push(system);

    // 按优先级排序
    this.systems.sort((a, b) => a.getPriority() - b.getPriority());

    // 初始化系统
    system.initialize();

    // 发出系统添加事件
    this.emit('systemAdded', system);

    return system;
  }

  /**
   * 获取系统
   * @param systemClass 系统类
   * @returns 系统实例，如果不存在则返回null
   */
  public getSystem<T extends System>(systemClass: new (...args: any[]) => T): T | null {
    for (const system of this.systems) {
      if (system instanceof systemClass) {
        return system as T;
      }
    }
    return null;
  }

  /**
   * 移除系统
   * @param system 系统实例或类
   * @returns 是否成功移除
   */
  public removeSystem(system: System | (new (...args: any[]) => System)): boolean {
    let index = -1;

    if (typeof system === 'function') {
      // 如果是类，查找该类的实例
      index = this.systems.findIndex(s => s instanceof system);
    } else {
      // 如果是实例，直接查找
      index = this.systems.indexOf(system);
    }

    if (index !== -1) {
      const removedSystem = this.systems[index];
      (removedSystem as any).dispose();
      this.systems.splice(index, 1);

      // 发出系统移除事件
      this.emit('systemRemoved', removedSystem);

      return true;
    }

    return false;
  }

  /**
   * 获取所有系统
   * @returns 系统数组
   */
  public getSystems(): System[] {
    return [...this.systems];
  }

  /**
   * 获取所有实体
   * @returns 实体映射
   */
  public getEntities(): Map<string, Entity> {
    return this.entities;
  }

  /**
   * 清空世界
   */
  public clear(): void {
    // 移除所有系统
    for (const system of Array.from(this.systems)) {
      (system as any).dispose();
    }
    this.systems.length = 0;

    // 移除所有实体
    for (const entity of Array.from(this.entities.values())) {
      (entity as any).dispose();
    }
    this.entities.clear();

    // 移除所有场景
    for (const scene of Array.from(this.scenes.values())) {
      (scene as any).dispose();
    }
    this.scenes.clear();

    // 清除活跃场景
    this.activeScene = null;

    // 发出清空事件
    this.emit('cleared');
  }

  /**
   * 销毁世界
   */
  public dispose(): void {
    // 清空世界
    this.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
