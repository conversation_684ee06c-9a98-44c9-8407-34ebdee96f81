/**
 * 变换组件
 * 处理实体的位置、旋转和缩放
 */
import * as THREE from 'three';
import { Component } from '../core/Component';
export declare class Transform extends Component {
    /** 组件类型 */
    static readonly type: string;
    /** Three.js对象 */
    private object3D;
    /** 本地位置 */
    private localPosition;
    /** 本地旋转 */
    private localRotation;
    /** 本地旋转四元数 */
    private localQuaternion;
    /** 本地缩放 */
    private localScale;
    /** 世界位置 */
    private worldPosition;
    /** 世界旋转 */
    private worldRotation;
    /** 世界旋转四元数 */
    private worldQuaternion;
    /** 世界缩放 */
    private worldScale;
    /** 父变换 */
    private parent;
    /** 子变换列表 */
    private children;
    /** 是否需要更新 */
    private dirty;
    /**
     * 创建变换组件
     */
    constructor(options?: {
        position?: {
            x: number;
            y: number;
            z: number;
        } | THREE.Vector3;
        rotation?: {
            x: number;
            y: number;
            z: number;
        } | THREE.Euler;
        scale?: {
            x: number;
            y: number;
            z: number;
        } | THREE.Vector3;
    });
    /**
     * 当组件附加到实体时调用
     */
    protected onAttach(): void;
    /**
     * 设置本地位置
     * @param x X坐标或位置向量
     * @param y Y坐标
     * @param z Z坐标
     */
    setPosition(x: number | THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, y?: number, z?: number): void;
    /**
     * 获取本地位置
     * @returns 本地位置
     */
    getPosition(): THREE.Vector3;
    /**
     * 位置属性 getter
     */
    get position(): {
        x: number;
        y: number;
        z: number;
    };
    /**
     * 设置世界位置
     * @param x X坐标或位置向量
     * @param y Y坐标
     * @param z Z坐标
     */
    setWorldPosition(x: number | THREE.Vector3, y?: number, z?: number): void;
    /**
     * 获取世界位置
     * @returns 世界位置
     */
    getWorldPosition(): THREE.Vector3;
    /**
     * 设置本地旋转
     * @param x X轴旋转角度（弧度）或欧拉角
     * @param y Y轴旋转角度（弧度）
     * @param z Z轴旋转角度（弧度）
     * @param order 旋转顺序
     */
    setRotation(x: number | THREE.Euler | {
        x: number;
        y: number;
        z: number;
    }, y?: number, z?: number, order?: THREE.EulerOrder): void;
    /**
     * 设置本地旋转四元数
     * @param x X分量或四元数
     * @param y Y分量
     * @param z Z分量
     * @param w W分量
     */
    setRotationQuaternion(x: number | THREE.Quaternion, y?: number, z?: number, w?: number): void;
    /**
     * 获取本地旋转四元数
     * @returns 本地旋转四元数
     */
    getRotationQuaternion(): THREE.Quaternion;
    /**
     * 获取本地旋转
     * @returns 本地旋转
     */
    getRotation(): THREE.Euler;
    /**
     * 旋转属性 getter
     */
    get rotation(): {
        x: number;
        y: number;
        z: number;
    };
    /**
     * 设置世界旋转
     * @param x X轴旋转角度（弧度）或欧拉角
     * @param y Y轴旋转角度（弧度）
     * @param z Z轴旋转角度（弧度）
     * @param order 旋转顺序
     */
    setWorldRotation(x: number | THREE.Euler, y?: number, z?: number, order?: THREE.EulerOrder): void;
    /**
     * 设置世界旋转四元数
     * @param x X分量或四元数
     * @param y Y分量
     * @param z Z分量
     * @param w W分量
     */
    setWorldRotationQuaternion(x: number | THREE.Quaternion, y?: number, z?: number, w?: number): void;
    /**
     * 获取世界旋转四元数
     * @returns 世界旋转四元数
     */
    getWorldRotationQuaternion(): THREE.Quaternion;
    /**
     * 获取世界旋转
     * @returns 世界旋转
     */
    getWorldRotation(): THREE.Euler;
    /**
     * 设置本地缩放
     * @param x X轴缩放或缩放向量
     * @param y Y轴缩放
     * @param z Z轴缩放
     */
    setScale(x: number | THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, y?: number, z?: number): void;
    /**
     * 获取本地缩放
     * @returns 本地缩放
     */
    getScale(): THREE.Vector3;
    /**
     * 缩放属性 getter
     */
    get scale(): {
        x: number;
        y: number;
        z: number;
    };
    /**
     * 设置世界缩放
     * @param x X轴缩放或缩放向量
     * @param y Y轴缩放
     * @param z Z轴缩放
     */
    setWorldScale(x: number | THREE.Vector3, y?: number, z?: number): void;
    /**
     * 获取世界缩放
     * @returns 世界缩放
     */
    getWorldScale(): THREE.Vector3;
    /**
     * 向前移动
     * @param distance 距离
     */
    moveForward(distance: number): void;
    /**
     * 向右移动
     * @param distance 距离
     */
    moveRight(distance: number): void;
    /**
     * 向上移动
     * @param distance 距离
     */
    moveUp(distance: number): void;
    /**
     * 绕X轴旋转
     * @param angle 角度（弧度）
     */
    rotateX(angle: number): void;
    /**
     * 绕Y轴旋转
     * @param angle 角度（弧度）
     */
    rotateY(angle: number): void;
    /**
     * 绕Z轴旋转
     * @param angle 角度（弧度）
     */
    rotateZ(angle: number): void;
    /**
     * 注视点
     * @param target 目标位置
     * @param up 上方向
     */
    lookAt(target: THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    } | number, y?: number, z?: number, up?: THREE.Vector3): void;
    /**
     * 设置父变换
     * @param parent 父变换
     */
    setParent(parent: Transform | null): void;
    /**
     * 获取父变换
     * @returns 父变换
     */
    getParent(): Transform | null;
    /**
     * 获取子变换列表
     * @returns 子变换数组
     */
    getChildren(): Transform[];
    /**
     * 获取本地矩阵
     * @returns 本地矩阵
     */
    getLocalMatrix(): THREE.Matrix4;
    /**
     * 获取世界矩阵
     * @returns 世界矩阵
     */
    getWorldMatrix(): THREE.Matrix4;
    /**
     * 更新世界矩阵
     */
    private updateWorldMatrix;
    /**
     * 获取Three.js对象
     * @returns Three.js对象
     */
    getObject3D(): THREE.Object3D;
    /**
     * 平移
     * @param x X轴偏移或偏移向量
     * @param y Y轴偏移
     * @param z Z轴偏移
     */
    translate(x: number | THREE.Vector3 | {
        x: number;
        y: number;
        z: number;
    }, y?: number, z?: number): void;
    /**
     * 旋转
     * @param x X轴旋转角度（弧度）或旋转向量
     * @param y Y轴旋转角度（弧度）
     * @param z Z轴旋转角度（弧度）
     */
    rotate(x: number | {
        x: number;
        y: number;
        z: number;
    }, y?: number, z?: number): void;
    /**
     * 获取前方向
     * @returns 前方向向量
     */
    getForward(): THREE.Vector3;
    /**
     * 获取右方向
     * @returns 右方向向量
     */
    getRight(): THREE.Vector3;
    /**
     * 获取上方向
     * @returns 上方向向量
     */
    getUp(): THREE.Vector3;
    /**
     * 缩放（乘法操作）
     * @param x X轴缩放因子或缩放向量
     * @param y Y轴缩放因子
     * @param z Z轴缩放因子
     */
    scale3d(x: number | {
        x: number;
        y: number;
        z: number;
    }, y?: number, z?: number): void;
    /**
     * 销毁组件
     */
    dispose(): void;
}
