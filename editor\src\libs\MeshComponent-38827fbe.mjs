var d = Object.defineProperty;
var n = (h, i, e) => i in h ? d(h, i, { enumerable: !0, configurable: !0, writable: !0, value: e }) : h[i] = e;
var s = (h, i, e) => (n(h, typeof i != "symbol" ? i + "" : i, e), e);
import * as r from "three";
import { Component as o, EventEmitter as c } from "./dl-engine.js";
import "cannon-es";
import "uuid";
const a = class a extends o {
  /**
   * 构造函数
   * @param options 选项
   */
  constructor(e = {}) {
    super(a.type);
    /** 网格对象 */
    s(this, "mesh");
    /** 几何体 */
    s(this, "geometry");
    /** 材质 */
    s(this, "material");
    /** 是否启用LOD */
    s(this, "enableLOD");
    /** LOD对象 */
    s(this, "lod", null);
    /** 事件发射器 */
    s(this, "events", new c());
    /** 是否启用实例化渲染 */
    s(this, "instanced");
    /** 实例数量 */
    s(this, "instanceCount");
    if (this.geometry = e.geometry || new r.BoxGeometry(1, 1, 1), e.material)
      this.material = e.material;
    else {
      const t = e.materialOptions || { color: 13421772 };
      this.material = new r.MeshStandardMaterial(t);
    }
    this.mesh = new r.Mesh(this.geometry, this.material), this.mesh.visible = e.visible !== void 0 ? e.visible : !0, this.mesh.receiveShadow = e.receiveShadow !== void 0 ? e.receiveShadow : !0, this.mesh.castShadow = e.castShadow !== void 0 ? e.castShadow : !0, this.mesh.frustumCulled = e.frustumCulled !== void 0 ? e.frustumCulled : !0, this.mesh.renderOrder = e.renderOrder || 0, this.instanced = e.instanced || !1, this.instanceCount = e.instanceCount || 1, this.enableLOD = e.enableLOD || !1, this.enableLOD && e.lodLevels && e.lodLevels.length > 0 && this.setupLOD(e.lodLevels);
  }
  /**
   * 设置LOD
   * @param lodLevels LOD级别
   */
  setupLOD(e) {
    this.lod = new r.LOD(), this.lod.addLevel(this.mesh, 0);
    for (const t of e) {
      const l = new r.Mesh(t.geometry, this.material);
      l.castShadow = this.mesh.castShadow, l.receiveShadow = this.mesh.receiveShadow, this.lod.addLevel(l, t.distance);
    }
  }
  /**
   * 设置材质
   * @param material 材质
   */
  setMaterial(e) {
    this.material = e, this.mesh.material = e, this.events.emit("materialChanged", e);
  }
  /**
   * 设置几何体
   * @param geometry 几何体
   */
  setGeometry(e) {
    this.geometry = e, this.mesh.geometry = e, this.events.emit("geometryChanged", e);
  }
  /**
   * 获取包围盒
   * @param target 目标包围盒
   * @returns 包围盒
   */
  getBoundingBox(e) {
    const t = e || new r.Box3();
    return this.geometry.computeBoundingBox(), t.copy(this.geometry.boundingBox).applyMatrix4(this.mesh.matrixWorld), t;
  }
  /**
   * 获取包围球
   * @param target 目标包围球
   * @returns 包围球
   */
  getBoundingSphere(e) {
    const t = e || new r.Sphere();
    return this.geometry.computeBoundingSphere(), t.copy(this.geometry.boundingSphere), t.radius *= this.mesh.matrixWorld.getMaxScaleOnAxis(), t.center.applyMatrix4(this.mesh.matrixWorld), t;
  }
};
/** 组件类型 */
s(a, "type", "MeshComponent");
let m = a;
export {
  m as MeshComponent
};
